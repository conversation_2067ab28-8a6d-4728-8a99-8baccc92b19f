# 图像重建与指标计算

本文档说明如何使用修改后的 `recon_custom.py` 进行图像生成和指标计算。

## 主要修改

### 1. 适配 eval.py 的指标计算方法

- 修改了 `cal_metrics` 函数，使其兼容 eval.py 中的完整指标计算
- 支持以下指标：
  - **PixCorr**: 像素相关性
  - **SSIM**: 结构相似性指数
  - **AlexNet(2)**: AlexNet features.4 层的二路识别准确率
  - **AlexNet(5)**: AlexNet features.11 层的二路识别准确率
  - **InceptionV3**: InceptionV3 avgpool 层的二路识别准确率
  - **CLIP**: CLIP ViT-L/14 的二路识别准确率
  - **EffNet-B**: EfficientNet-B1 的相关距离
  - **SwAV**: SwAV ResNet50 的相关距离

### 2. 数据格式兼容性

- 添加了 `save_tensor_images` 函数，将生成的图像保存为 `.pt` 格式
- 支持加载参考图像并转换为相应格式
- 兼容 eval.py 期望的 `_img.pt` 和 `_rec.pt` 文件命名格式

### 3. 新增功能

- `load_reference_images`: 加载参考图像
- `create_dummy_reference_images`: 创建虚拟参考图像用于测试
- `two_way_identification`: 二路识别任务实现

## 使用方法

### 方法1: 直接运行修改后的 recon_custom.py

```bash
python reconstruction/recon_custom.py
```

### 方法2: 使用示例脚本

```bash
python reconstruction/example_usage_with_metrics.py
```

### 方法3: 在代码中调用

```python
from recon_custom import Generator4Embeds, cal_metrics, save_tensor_images

# 1. 生成图像
device = 'cuda:0'
generator = Generator4Embeds(num_inference_steps=5, device=device)

# 2. 保存为.pt格式
generated_tensors = torch.stack(all_generated_images)
save_tensor_images(generated_tensors, output_dir, prefix="rec")

# 3. 准备参考图像（可选）
reference_tensors = load_reference_images(reference_path)
save_tensor_images(reference_tensors, output_dir, prefix="img")

# 4. 计算指标
results = cal_metrics(output_dir, device)
```

## 配置选项

### 主要参数

- `data_path`: EEG嵌入数据路径
- `output_dir`: 输出目录
- `reference_images_path`: 参考图像路径（可选）
- `max_samples`: 最大处理样本数
- `device`: 计算设备

### 生成器参数

- `num_inference_steps`: 推理步数
- `img2img_strength`: 图像到图像强度
- `ip_adapter_scale`: IP适配器缩放

## 输出文件

### 生成的文件

1. **PNG格式**: `generated{i}.png` - 用于可视化查看
2. **PT格式**: `{index:06d}_rec.pt` - 用于指标计算
3. **参考图像**: `{index:06d}_img.pt` - 参考图像（如果提供）
4. **指标结果**: `_metrics_on_{N}samples.csv` - 计算结果

### 指标结果格式

```
Metric          Value
PixCorr         0.1234
SSIM            0.5678
AlexNet(2)      0.2345
AlexNet(5)      0.3456
InceptionV3     0.4567
CLIP            0.5678
EffNet-B        0.6789
SwAV            0.7890
```

## 依赖要求

### 必需依赖

```bash
pip install torch torchvision diffusers transformers
pip install numpy pandas scipy tqdm pillow
```

### 可选依赖（用于完整指标计算）

```bash
pip install scikit-image  # SSIM, PSNR
pip install lpips         # LPIPS
pip install clip-by-openai # CLIP
```

## 注意事项

1. **内存使用**: 大批量处理时注意GPU内存使用
2. **参考图像**: 如果没有真实参考图像，会自动创建虚拟参考图像
3. **指标计算**: 某些指标需要特定的依赖库，缺失时会跳过
4. **文件格式**: 确保输入数据格式正确（.npy文件包含EEG嵌入）

## 故障排除

### 常见问题

1. **CUDA内存不足**: 减少 `max_samples` 或使用更小的 `num_inference_steps`
2. **依赖缺失**: 安装相应的可选依赖库
3. **文件路径错误**: 检查数据路径和输出路径是否正确

### 调试模式

设置较小的 `max_samples` 值进行测试：

```python
config = {
    'max_samples': 10,  # 仅处理10个样本用于测试
    # ... 其他配置
}
```

## 性能优化

1. **批处理**: 可以修改代码支持批量生成以提高效率
2. **模型缓存**: 避免重复加载模型
3. **并行计算**: 某些指标计算可以并行化

## 扩展功能

可以根据需要添加更多指标：

1. **FID**: Fréchet Inception Distance
2. **IS**: Inception Score
3. **自定义指标**: 根据具体需求实现

